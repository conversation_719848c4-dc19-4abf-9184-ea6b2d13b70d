const Joi = require('joi');

const registerSchema = Joi.object({
    name: Joi.string().min(3).max(30).optional().messages({
        'string.base': 'Name must be a string',
        'string.empty': 'Name is required',
        'string.min': 'Name should have at least 3 characters',
        'any.required': 'Name is optional'
    }),
    user_name: Joi.string().min(8).max(50).pattern(/^[a-zA-Z0-9_]+$/).required().messages({
        'string.empty': 'Username is required',
        'string.min': 'Username should have at least 8 characters',
        'string.max': 'Username should not exceed 50 characters',
        'string.pattern.base': 'Username can only contain letters, numbers, and underscores',
        'any.required': 'Username is required'
    }),
    mobile: Joi.string().min(10).max(15).required().messages({
        'string.empty': 'Mobile number is required',
        'string.min': 'Mobile number should be at least 10 digits',
        'any.required': 'Mobile number is required'
    }),
    email: Joi.string().email().required().messages({
        'string.empty': 'Email is required',
        'string.email': 'Email must be a valid email address',
        'any.required': 'Email is required'
    }),
    password: Joi.string().min(8).required().messages({
        'string.empty': 'Password is required',
        'string.min': 'Password should be at least 8 characters long',
        'any.required': 'Password is required'
    }),
    referral_code: Joi.string().allow('').optional(),
    confirm_password: Joi.any().valid(Joi.ref('password')).required().messages({
        'any.only': 'Confirm password must match password',
        'any.required': 'Confirm password is required'
    })
});

const loginSchema = Joi.object({
    user_name: Joi.string().min(8).max(50).required().messages({
        'string.empty': 'Username is required',
        'string.min': 'Username should have at least 8 characters',
        'any.required': 'Username is required'
    }),
    password: Joi.string().min(8).required().messages({
        'string.empty': 'Password is required',
        'string.min': 'Password should be at least 8 characters long',
        'any.required': 'Password is required'
    })
});

const checkUsernameSchema = Joi.object({
    user_name: Joi.string().min(8).max(50).required().messages({
        'string.empty': 'Username is required',
        'string.min': 'Username should have at least 8 characters',
        'any.required': 'Username is required'
    })
});

const updatePasswordSchema = Joi.object({
    current_password: Joi.string().min(8).required().messages({
        'string.empty': 'Current password is required',
        'string.min': 'Current password should be at least 8 characters long',
        'any.required': 'Current password is required'
    }),
    new_password: Joi.string().min(8).required().messages({
        'string.empty': 'New password is required',
        'string.min': 'New password should be at least 8 characters long',
        'any.required': 'New password is required'
    }),
    confirm_password: Joi.any().valid(Joi.ref('new_password')).required().messages({
        'any.only': 'Confirm password must match new password',
        'any.required': 'Confirm password is required'
    })
});

const forgotPasswordSchema = Joi.object({
    email: Joi.string().email().required().messages({
        'string.empty': 'Email is required',
        'string.email': 'Email must be a valid email address',
        'any.required': 'Email is required'
    })
});

const resetPasswordSchema = Joi.object({
    token: Joi.string().required().messages({
        'string.empty': 'Reset token is required',
        'any.required': 'Reset token is required'
    }),
    new_password: Joi.string().min(8).required().messages({
        'string.empty': 'New password is required',
        'string.min': 'New password should be at least 8 characters long',
        'any.required': 'New password is required'
    }),
    confirm_password: Joi.any().valid(Joi.ref('new_password')).required().messages({
        'any.only': 'Confirm password must match new password',
        'any.required': 'Confirm password is required'
    })
});

module.exports = {
    registerSchema,
    loginSchema,
    checkUsernameSchema,
    updatePasswordSchema,
    forgotPasswordSchema,
    resetPasswordSchema,
};
