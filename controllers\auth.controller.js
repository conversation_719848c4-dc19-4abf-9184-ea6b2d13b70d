const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const Joi = require('joi');
const crypto = require('crypto');
const { User, Wallet, Referral, PasswordResetToken } = require('../models');
const { Op } = require('sequelize');
const BaseController = require('../helpers/BaseController');
const { registerSchema, loginSchema, checkUsernameSchema, updatePasswordSchema, forgotPasswordSchema, resetPasswordSchema } = require('../validations/auth.validation');
const emailService = require('../services/emailService');

exports.register = async (req, res) => {
    try {
        // Validate input
        const { error, value } = registerSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { name, user_name, email, mobile, password, referral_code } = value;

        // Check email and username uniqueness
        const emailUser = await User.findOne({ where: { email } });
        const usernameUser = await User.findOne({ where: { user_name } });

        const duplicateErrors = [];
        if (emailUser) duplicateErrors.push('Email already exists');
        if (usernameUser) duplicateErrors.push('Username already exists');

        if (duplicateErrors.length > 0) {
            return BaseController.sendError(res, duplicateErrors[0]);
        }

        // Start transaction
        const transaction = await User.sequelize.transaction();

        try {
            // Handle referral if provided
            let referrer = null;
            if (referral_code?.trim()) {
                referrer = await User.findOne({
                    where: { referral_code: referral_code.trim() },
                    transaction
                });

                if (!referrer) {
                    await transaction.rollback();
                    return BaseController.sendError(res, 'Invalid referral code');
                }
            }

            // Create new user
            const newUser = await User.create({
                name,
                user_name,
                email,
                mobile,
                password,
                referral_code: '',
                referred_by: referrer?.user_id || null
            }, { transaction });

            // Create wallet
            const newWallet = await Wallet.create({
                user_id: newUser.user_id,
                balance: 0.00,
                bonus_balance: 0.00,
                total_deposited: 0.00,
                total_withdrawn: 0.00,
                total_invested: 0.00,
                is_active: true
            }, { transaction });

            // Referral tree
            if (referrer) {
                await Referral.createReferralTree(newUser.user_id, referral_code, transaction);
            }

            await transaction.commit();

            // Generate token
            const token = jwt.sign(
                {
                    id: newUser.user_id,
                    user_id: newUser.user_id,
                    user_name: newUser.user_name,
                    email: newUser.email,
                    role: newUser.role || 'user'
                },
                process.env.JWT_SECRET || 'mysecretkey',
                { expiresIn: '1d' }
            );

            // Success response
            return BaseController.sendResponse(res, {
                token,
                user: {
                    user_id: newUser.user_id,
                    name,
                    user_name,
                    email,
                    mobile,
                    referral_code: newUser.referral_code,
                    wallet: {
                        balance: newWallet.balance,
                        bonus_balance: newWallet.bonus_balance
                    }
                }
            }, 'User registered successfully');

        } catch (transactionError) {
            await transaction.rollback();
            console.error('Transaction error:', transactionError);
            return BaseController.sendError(res, 'Transaction failed', [{ error: transactionError.message }]);
        }

    } catch (error) {
        console.error('Register error:', error);
        return BaseController.sendError(res, 'Registration failed.', [{ error: error.message }]);
    }
};

exports.login = async (req, res) => {
    try {
        // Validate input
        const { error, value } = loginSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { user_name, password } = value;
        const user = await User.findOne({ where: { user_name } });

        if (!user || !(await user.verify_password(password))) {
            return BaseController.sendError(res, 'Invalid credentials', 401);
        }

        const token = jwt.sign({
            id: user.user_id,
            user_id: user.user_id,
            user_name: user.user_name,
            email: user.email,
            role: user.role
        }, process.env.JWT_SECRET || 'mysecretkey', {
            expiresIn: '1d'
        });

        return BaseController.sendResponse(res, {
            token,
            user: {
                user_id: user.user_id,
                name: user.name,
                user_name: user.user_name,
                email: user.email,
                mobile: user.mobile
            }
        }, 'Login successful');
    } catch (error) {
        console.error('Login error:', error);
        return BaseController.sendError(res, 'Something went wrong', 500);
    }
};

exports.checkUsername = async (req, res) => {
    try {
        // Validate input
        const { error, value } = checkUsernameSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { user_name } = value;
        const user = await User.findOne({ where: { user_name } });

        if (user) {
            return BaseController.sendResponse(res, null, 'Username already exists');
        } else {
            return BaseController.sendResponse(res, null, 'Username is available');
        }
    } catch (error) {
        console.error('Check username error:', error);
        return BaseController.sendError(res, 'Something went wrong', 500);
    }
};

exports.updatePassword = async (req, res) => {
    try {
        // Validate input
        const { error, value } = updatePasswordSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { current_password, new_password } = value;

        // Get current user from authentication middleware (req.user.id)
        const userId = req.user.id;

        // Find user by ID
        const user = await User.findByPk(userId);

        if (!user) {
            return BaseController.sendError(res, 'User not found', 404);
        }

        // Verify current password
        const isCurrentPasswordValid = await user.verify_password(current_password);
        if (!isCurrentPasswordValid) {
            return BaseController.sendError(res, 'Current password is incorrect', 401);
        }

        // Check if new password is different from current password
        const isSamePassword = await user.verify_password(new_password);
        if (isSamePassword) {
            return BaseController.sendError(res, 'New password must be different from current password');
        }

        // Update password (the beforeUpdate hook will hash it)
        await user.update({ password: new_password });

        return BaseController.sendResponse(res, null, 'Password updated successfully');

    } catch (error) {
        console.error('Update password error:', error);
        return BaseController.sendError(res, 'Something went wrong', 500);
    }
};

exports.forgotPassword = async (req, res) => {
    try {
        // Validate input
        const { error, value } = forgotPasswordSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { email } = value;

        // Find user by email
        const user = await User.findOne({ where: { email } });

        // Always return success message for security (don't reveal if email exists)
        if (!user) {
            return BaseController.sendResponse(res, null, 'If your email is registered, you will receive a password reset link shortly');
        }

        // Generate secure random token
        const resetToken = crypto.randomBytes(32).toString('hex');
        const expiresAt = new Date(Date.now() + parseInt(process.env.RESET_TOKEN_EXPIRY || 3600000)); // 1 hour default

        // Clean up any existing tokens for this user
        await PasswordResetToken.destroy({
            where: { user_id: user.user_id }
        });

        // Create new reset token
        await PasswordResetToken.create({
            user_id: user.user_id,
            token: resetToken,
            expires_at: expiresAt
        });

        // Send password reset email in background (non-blocking)
        emailService.sendPasswordResetEmailBackground(
            user.email,
            user.user_name || user.user_name,
            resetToken
        );

        // Return immediately without waiting for email to be sent
        return BaseController.sendResponse(res, null, 'If your email is registered, you will receive a password reset link shortly');

    } catch (error) {
        console.error('Forgot password error:', error);
        return BaseController.sendError(res, 'Something went wrong', 500);
    }
};

exports.resetPassword = async (req, res) => {
    try {
        // Validate input
        const { error, value } = resetPasswordSchema.validate(req.body, { abortEarly: false });

        if (error) {
            const firstError = error.details[0];
            return BaseController.sendError(res, firstError.message);
        }

        const { token, new_password } = value;

        // Find the reset token
        const resetToken = await PasswordResetToken.findOne({
            where: { token },
            include: [{
                model: User,
                as: 'user'
            }]
        });

        if (!resetToken) {
            return BaseController.sendError(res, 'Invalid or expired reset token', 400);
        }

        // Check if token is valid (not used and not expired)
        if (!resetToken.isValid()) {
            return BaseController.sendError(res, 'Invalid or expired reset token', 400);
        }

        // Update user password (the beforeUpdate hook will hash it)
        await resetToken.user.update({ password: new_password });

        // Mark token as used
        await resetToken.update({ used: true });

        // Clean up expired tokens (optional cleanup)
        PasswordResetToken.cleanupExpiredTokens().catch(err => {
            console.error('Error cleaning up expired tokens:', err);
        });

        return BaseController.sendResponse(res, null, 'Password reset successfully');

    } catch (error) {
        console.error('Reset password error:', error);
        return BaseController.sendError(res, 'Something went wrong', 500);
    }
};