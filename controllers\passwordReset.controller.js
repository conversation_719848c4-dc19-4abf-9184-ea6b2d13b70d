const path = require('path');
const fs = require('fs');
const authController = require('./auth.controller');

class PasswordResetController {
    
    // Show password reset form
    static showResetForm(req, res) {
        try {
            const token = req.query.token;
            if (!token) {
                return res.status(400).send('Reset token is required');
            }

            // Read the HTML template
            let htmlContent = fs.readFileSync(path.join(__dirname, '../templates', 'resetPasswordForm.html'), 'utf8');

            // Replace the token placeholder
            htmlContent = htmlContent.replace('{{TOKEN}}', token);

            res.send(htmlContent);
        } catch (error) {
            console.error('Error serving reset password form:', error);
            res.status(500).send('Internal server error');
        }
    }

    // Handle form submission for password reset
    static async handleResetSubmission(req, res) {
        try {
            console.log('Reset password form submitted:', req.body);

            // Check if req.body exists
            if (!req.body) {
                console.error('req.body is undefined');
                return res.status(400).send('Form data not received properly');
            }

            const { token, new_password, confirm_password } = req.body;

            // Basic validation
            if (!token || !new_password || !confirm_password) {
                console.log('Missing fields:', { token: !!token, new_password: !!new_password, confirm_password: !!confirm_password });
                return res.status(400).send(PasswordResetController.getResetFormWithMessage('All fields are required', 'error', token || ''));
            }

            if (new_password !== confirm_password) {
                return res.status(400).send(PasswordResetController.getResetFormWithMessage('Passwords do not match', 'error', token));
            }

            if (new_password.length < 8) {
                return res.status(400).send(PasswordResetController.getResetFormWithMessage('Password must be at least 8 characters long', 'error', token));
            }

            // Create a mock request/response for the controller
            const mockReq = { body: { token, new_password, confirm_password } };
            let controllerResult = null;
            let statusCode = 200;

            const mockRes = {
                status: (code) => {
                    statusCode = code;
                    return mockRes;
                },
                json: (data) => {
                    controllerResult = data;
                    return mockRes;
                }
            };

            console.log('Calling auth controller with:', mockReq.body);
            await authController.resetPassword(mockReq, mockRes);
            console.log('Controller result:', { statusCode, controllerResult });

            if (statusCode === 200 && controllerResult?.success) {
                // Success - show success page
                console.log('Password reset successful, showing success page');
                return res.send(PasswordResetController.getSuccessPage());
            } else {
                // Error from controller
                console.log('Password reset failed:', controllerResult);
                const errorMessage = controllerResult?.message || 'Password reset failed';
                return res.status(statusCode).send(PasswordResetController.getResetFormWithMessage(errorMessage, 'error', token));
            }

        } catch (error) {
            console.error('Error processing password reset:', error);
            return res.status(500).send(PasswordResetController.getResetFormWithMessage('Internal server error', 'error', req.body?.token || ''));
        }
    }

    // Helper function to get reset form with message
    static getResetFormWithMessage(message, messageType, token) {
        const path = require('path');
        const fs = require('fs');

        let htmlContent = fs.readFileSync(path.join(__dirname, '../templates', 'resetPasswordForm.html'), 'utf8');
        htmlContent = htmlContent.replace('{{TOKEN}}', token || '');

        // Replace template placeholders with actual message
        htmlContent = htmlContent.replace('{{#if message}}', '');
        htmlContent = htmlContent.replace('{{/if}}', '');
        htmlContent = htmlContent.replace('{{message}}', message);
        htmlContent = htmlContent.replace('{{messageType}}', messageType);

        return htmlContent;
    }

    // Helper function to get success page
    static getSuccessPage() {
        try {
            const successPagePath = path.join(__dirname, '../templates', 'passwordResetSuccess.html');
            return fs.readFileSync(successPagePath, 'utf8');
        } catch (error) {
            console.error('Error reading success template:', error);
            return '<h1>Password Reset Successful!</h1><p>Your password has been reset successfully.</p>';
        }
    }
}

module.exports = PasswordResetController;
